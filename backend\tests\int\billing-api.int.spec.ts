import { getPayload, Payload } from 'payload'
import config from '@/payload.config'
import { describe, it, beforeAll, afterAll, beforeEach, expect } from 'vitest'

let payload: Payload
let testUser: any
let testPatient: any
let testAppointment: any
let testTreatment: any

describe('Billing Collections Integration Tests', () => {
  beforeAll(async () => {
    const payloadConfig = await config
    payload = await getPayload({ config: payloadConfig })

    // Create test data
    testUser = await payload.create({
      collection: 'users',
      data: {
        email: '<EMAIL>',
        role: 'admin',
        firstName: 'Test',
        lastName: 'User',
        clerkId: 'test-clerk-id',
      },
    })

    testPatient = await payload.create({
      collection: 'patients',
      data: {
        fullName: '测试患者',
        phone: '13800138000',
        email: '<EMAIL>',
        medicalNotes: '无过敏史',
      },
    })

    testTreatment = await payload.create({
      collection: 'treatments',
      data: {
        name: '测试治疗',
        description: '测试治疗描述',
        defaultPrice: 500,
        defaultDurationInMinutes: 60,
      },
    })

    testAppointment = await payload.create({
      collection: 'appointments',
      data: {
        appointmentDate: new Date('2024-07-10T10:00:00Z'),
        status: 'scheduled',
        treatment: testTreatment.id,
        price: 500,
        durationInMinutes: 60,
        patient: testPatient.id,
        practitioner: testUser.id,
      },
    })
  })

  afterAll(async () => {
    // Clean up test data
    if (testAppointment) await payload.delete({ collection: 'appointments', id: testAppointment.id })
    if (testTreatment) await payload.delete({ collection: 'treatments', id: testTreatment.id })
    if (testPatient) await payload.delete({ collection: 'patients', id: testPatient.id })
    if (testUser) await payload.delete({ collection: 'users', id: testUser.id })
  })

  describe('Bills Collection', () => {
    let testBill: any

    beforeEach(async () => {
      // Clean up any existing bills
      const existingBills = await payload.find({ collection: 'bills' })
      for (const bill of existingBills.docs) {
        await payload.delete({ collection: 'bills', id: bill.id })
      }
    })

    describe('Create Bill', () => {
      it('should create a new bill successfully', async () => {
        const billData = {
          patient: testPatient.id,
          appointment: testAppointment.id,
          treatment: testTreatment.id,
          billType: 'treatment',
          description: '测试账单',
          subtotal: 500.00,
          discountAmount: 50.00,
          taxAmount: 0.00,
          totalAmount: 450.00,
          remainingAmount: 450.00,
          status: 'draft',
          dueDate: new Date('2024-12-31T00:00:00Z'),
          createdBy: testUser.id,
        }

        const result = await payload.create({
          collection: 'bills',
          data: billData,
        })

        expect(result.id).toBeDefined()
        expect(result.billNumber).toMatch(/^BILL-\d{4}-\d{3}$/)
        expect(result.patient).toBe(testPatient.id)
        expect(result.totalAmount).toBe(450.00)
        expect(result.status).toBe('draft')

        testBill = result
      })

      it('should validate required fields', async () => {
        const invalidBillData = {
          // Missing required fields
          billType: 'treatment',
          description: '测试账单',
        }

        try {
          await payload.create({
            collection: 'bills',
            data: invalidBillData,
          })
          expect(true).toBe(false) // Should not reach here
        } catch (error) {
          expect(error).toBeDefined()
        }
      })

      it('should validate amount fields', async () => {
        const invalidBillData = {
          patient: testPatient.id,
          billType: 'treatment',
          description: '测试账单',
          subtotal: -100, // Invalid negative amount
          totalAmount: -100,
          remainingAmount: -100,
          status: 'draft',
          dueDate: new Date('2024-12-31T00:00:00Z'),
          createdBy: testUser.id,
        }

        try {
          await payload.create({
            collection: 'bills',
            data: invalidBillData,
          })
          expect(true).toBe(false) // Should not reach here
        } catch (error) {
          expect(error).toBeDefined()
        }
      })
    })

    describe('Find Bills', () => {
      beforeEach(async () => {
        // Create test bills
        testBill = await payload.create({
          collection: 'bills',
          data: {
            patient: testPatient.id,
            billType: 'treatment',
            description: '测试账单',
            subtotal: 500.00,
            discountAmount: 0.00,
            taxAmount: 0.00,
            totalAmount: 500.00,
            remainingAmount: 500.00,
            status: 'confirmed',
            dueDate: new Date('2024-12-31'),
            createdBy: testUser.id,
          },
        })
      })

      it('should fetch bills with pagination', async () => {
        const result = await payload.find({
          collection: 'bills',
          page: 1,
          limit: 10,
        })

        expect(result.docs).toBeDefined()
        expect(result.totalDocs).toBeGreaterThan(0)
        expect(result.page).toBe(1)
        expect(result.limit).toBe(10)
      })

      it('should filter bills by status', async () => {
        const result = await payload.find({
          collection: 'bills',
          where: {
            status: {
              equals: 'confirmed',
            },
          },
        })

        expect(result.docs.every((bill: any) => bill.status === 'confirmed')).toBe(true)
      })

      it('should filter bills by patient', async () => {
        const result = await payload.find({
          collection: 'bills',
          where: {
            patient: {
              equals: testPatient.id,
            },
          },
        })

        expect(result.docs.every((bill: any) => bill.patient === testPatient.id)).toBe(true)
      })
    })

    describe('Find Bill by ID', () => {
      beforeEach(async () => {
        testBill = await payload.create({
          collection: 'bills',
          data: {
            patient: testPatient.id,
            billType: 'treatment',
            description: '测试账单',
            subtotal: 500.00,
            discountAmount: 0.00,
            taxAmount: 0.00,
            totalAmount: 500.00,
            remainingAmount: 500.00,
            status: 'confirmed',
            dueDate: new Date('2024-12-31'),
            createdBy: testUser.id,
          },
        })
      })

      it('should fetch a specific bill', async () => {
        const result = await payload.findByID({
          collection: 'bills',
          id: testBill.id,
          depth: 2,
        })

        expect(result.id).toBe(testBill.id)
        expect(result.patient).toBeDefined()
      })

      it('should throw error for non-existent bill', async () => {
        try {
          await payload.findByID({
            collection: 'bills',
            id: 'non-existent-id',
          })
          expect(true).toBe(false) // Should not reach here
        } catch (error) {
          expect(error).toBeDefined()
        }
      })
    })

    describe('Update Bill', () => {
      beforeEach(async () => {
        testBill = await payload.create({
          collection: 'bills',
          data: {
            patient: testPatient.id,
            billType: 'treatment',
            description: '测试账单',
            subtotal: 500.00,
            discountAmount: 0.00,
            taxAmount: 0.00,
            totalAmount: 500.00,
            remainingAmount: 500.00,
            status: 'draft',
            dueDate: new Date('2024-12-31'),
            createdBy: testUser.id,
          },
        })
      })

      it('should update bill status', async () => {
        const result = await payload.update({
          collection: 'bills',
          id: testBill.id,
          data: { status: 'confirmed' },
        })

        expect(result.status).toBe('confirmed')
      })

      it('should update bill amounts', async () => {
        const result = await payload.update({
          collection: 'bills',
          id: testBill.id,
          data: {
            remainingAmount: 250.00,
            discountAmount: 50.00,
          },
        })

        expect(result.remainingAmount).toBe(250.00)
        expect(result.discountAmount).toBe(50.00)
      })
    })

    describe('Delete Bill', () => {
      beforeEach(async () => {
        testBill = await payload.create({
          collection: 'bills',
          data: {
            patient: testPatient.id,
            billType: 'treatment',
            description: '测试账单',
            subtotal: 500.00,
            discountAmount: 0.00,
            taxAmount: 0.00,
            totalAmount: 500.00,
            remainingAmount: 500.00,
            status: 'draft',
            dueDate: new Date('2024-12-31'),
            createdBy: testUser.id,
          },
        })
      })

      it('should delete a bill', async () => {
        await payload.delete({
          collection: 'bills',
          id: testBill.id,
        })

        // Verify bill is deleted
        try {
          await payload.findByID({ collection: 'bills', id: testBill.id })
          expect(true).toBe(false) // Should not reach here
        } catch (error) {
          expect(error).toBeDefined()
        }
      })

      it('should be able to delete bills with different statuses', async () => {
        // Update bill to confirmed status
        await payload.update({
          collection: 'bills',
          id: testBill.id,
          data: { status: 'confirmed' },
        })

        // Should still be able to delete
        await payload.delete({
          collection: 'bills',
          id: testBill.id,
        })

        // Verify bill is deleted
        try {
          await payload.findByID({ collection: 'bills', id: testBill.id })
          expect(true).toBe(false) // Should not reach here
        } catch (error) {
          expect(error).toBeDefined()
        }
      })
    })
  })
})
