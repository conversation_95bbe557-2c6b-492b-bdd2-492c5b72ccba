import { getPayload, Payload } from 'payload'
import config from '@/payload.config'
import { describe, it, beforeAll, afterAll, beforeEach, expect } from 'vitest'

let payload: Payload
let testUser: any
let testPatient: any
let testBill: any
let testTreatment: any

describe('Deposits Collection Integration Tests', () => {
  beforeAll(async () => {
    const payloadConfig = await config
    payload = await getPayload({ config: payloadConfig })

    // Create test data
    testUser = await payload.create({
      collection: 'users',
      data: {
        email: '<EMAIL>',
        role: 'admin',
        firstName: 'Test',
        lastName: 'User',
        clerkId: 'test-clerk-id',
      },
    })

    testPatient = await payload.create({
      collection: 'patients',
      data: {
        fullName: '测试患者',
        phone: '13800138000',
        email: '<EMAIL>',
        medicalNotes: '无过敏史',
      },
    })

    testTreatment = await payload.create({
      collection: 'treatments',
      data: {
        name: '测试治疗',
        description: '测试治疗描述',
        defaultPrice: 500,
        defaultDurationInMinutes: 60,
      },
    })

    testBill = await payload.create({
      collection: 'bills',
      data: {
        patient: testPatient.id,
        treatment: testTreatment.id,
        billType: 'treatment',
        description: '测试账单',
        subtotal: 500.00,
        discountAmount: 0.00,
        taxAmount: 0.00,
        totalAmount: 500.00,
        remainingAmount: 500.00,
        status: 'confirmed',
        dueDate: new Date('2024-12-31'),
        createdBy: testUser.id,
      },
    })
  })

  afterAll(async () => {
    // Clean up test data
    if (testBill) await payload.delete({ collection: 'bills', id: testBill.id })
    if (testTreatment) await payload.delete({ collection: 'treatments', id: testTreatment.id })
    if (testPatient) await payload.delete({ collection: 'patients', id: testPatient.id })
    if (testUser) await payload.delete({ collection: 'users', id: testUser.id })
  })

  describe('Deposits Collection', () => {
    let testDeposit: any

    beforeEach(async () => {
      // Clean up any existing deposits
      const existingDeposits = await payload.find({ collection: 'deposits' })
      for (const deposit of existingDeposits.docs) {
        await payload.delete({ collection: 'deposits', id: deposit.id })
      }
    })

    describe('Create Deposit', () => {
      it('should create a new deposit successfully', async () => {
        const depositData = {
          patientId: testPatient.id,
          treatmentId: testTreatment.id,
          depositType: 'treatment',
          amount: 1000.00,
          status: 'active',
          usedAmount: 0.00,
          remainingAmount: 1000.00,
          depositDate: new Date(),
          purpose: '治疗预付款',
          notes: '年度套餐预付',
          expiryDate: new Date('2024-12-31T00:00:00Z'),
        }

        const result = await payload.create({
          collection: 'deposits',
          data: depositData,
        })

        expect(result.id).toBeDefined()
        expect(result.depositNumber).toMatch(/^DEP-\d{4}-\d{3}$/)
        expect(result.patientId).toBe(testPatient.id)
        expect(result.amount).toBe(1000.00)
        expect(result.status).toBe('active')
        expect(result.usedAmount).toBe(0)
        expect(result.remainingAmount).toBe(1000.00)

        testDeposit = result
      })

      it('should validate required fields', async () => {
        const invalidDepositData = {
          // Missing required fields
          depositType: 'treatment',
          amount: 500.00,
        }

        const request = new NextRequest('http://localhost:3000/api/deposits', {
          method: 'POST',
          body: JSON.stringify(invalidDepositData),
          headers: { 'Content-Type': 'application/json' },
        })

        const response = await createDeposit(request)
        expect(response.status).toBe(400)
      })

      it('should validate deposit amount', async () => {
        const invalidDepositData = {
          patientId: testPatient.id,
          depositType: 'treatment',
          amount: -100.00, // Invalid negative amount
          purpose: '测试预付款',
        }

        const request = new NextRequest('http://localhost:3000/api/deposits', {
          method: 'POST',
          body: JSON.stringify(invalidDepositData),
          headers: { 'Content-Type': 'application/json' },
        })

        const response = await createDeposit(request)
        expect(response.status).toBe(400)
      })

      it('should validate deposit type', async () => {
        const invalidDepositData = {
          patientId: testPatient.id,
          depositType: 'invalid-type', // Invalid deposit type
          amount: 500.00,
          purpose: '测试预付款',
        }

        const request = new NextRequest('http://localhost:3000/api/deposits', {
          method: 'POST',
          body: JSON.stringify(invalidDepositData),
          headers: { 'Content-Type': 'application/json' },
        })

        const response = await createDeposit(request)
        expect(response.status).toBe(400)
      })
    })

    describe('GET /api/deposits', () => {
      beforeEach(async () => {
        // Create test deposit
        testDeposit = await payload.create({
          collection: 'deposits',
          data: {
            patientId: testPatient.id,
            treatmentId: testTreatment.id,
            depositType: 'treatment',
            amount: 1000.00,
            status: 'active',
            usedAmount: 200.00,
            remainingAmount: 800.00,
            depositDate: new Date(),
            purpose: '治疗预付款',
            notes: '测试预付款',
          },
        })
      })

      it('should fetch deposits with pagination', async () => {
        const request = new NextRequest('http://localhost:3000/api/deposits?page=1&limit=10')
        const response = await getDeposits(request)
        const result = await response.json()

        expect(response.status).toBe(200)
        expect(result.docs).toBeDefined()
        expect(result.totalDocs).toBeGreaterThan(0)
        expect(result.page).toBe(1)
        expect(result.limit).toBe(10)
      })

      it('should filter deposits by patient ID', async () => {
        const request = new NextRequest(`http://localhost:3000/api/deposits?patientId=${testPatient.id}`)
        const response = await getDeposits(request)
        const result = await response.json()

        expect(response.status).toBe(200)
        expect(result.docs.every((deposit: any) => deposit.patientId === testPatient.id)).toBe(true)
      })

      it('should filter deposits by status', async () => {
        const request = new NextRequest('http://localhost:3000/api/deposits?status=active')
        const response = await getDeposits(request)
        const result = await response.json()

        expect(response.status).toBe(200)
        expect(result.docs.every((deposit: any) => deposit.status === 'active')).toBe(true)
      })
    })

    describe('GET /api/deposits/[id]', () => {
      beforeEach(async () => {
        testDeposit = await payload.create({
          collection: 'deposits',
          data: {
            patientId: testPatient.id,
            treatmentId: testTreatment.id,
            depositType: 'treatment',
            amount: 1000.00,
            status: 'active',
            usedAmount: 200.00,
            remainingAmount: 800.00,
            depositDate: new Date(),
            purpose: '治疗预付款',
            notes: '测试预付款',
          },
        })
      })

      it('should fetch a specific deposit', async () => {
        const request = new NextRequest(`http://localhost:3000/api/deposits/${testDeposit.id}`)
        const response = await getDeposit(request, { params: Promise.resolve({ id: testDeposit.id }) })
        const result = await response.json()

        expect(response.status).toBe(200)
        expect(result.id).toBe(testDeposit.id)
        expect(result.patientId).toBe(testPatient.id)
      })

      it('should return 404 for non-existent deposit', async () => {
        const request = new NextRequest('http://localhost:3000/api/deposits/non-existent-id')
        const response = await getDeposit(request, { params: Promise.resolve({ id: 'non-existent-id' }) })

        expect(response.status).toBe(404)
      })
    })

    describe('PATCH /api/deposits/[id]', () => {
      beforeEach(async () => {
        testDeposit = await payload.create({
          collection: 'deposits',
          data: {
            patientId: testPatient.id,
            treatmentId: testTreatment.id,
            depositType: 'treatment',
            amount: 1000.00,
            status: 'active',
            usedAmount: 0.00,
            remainingAmount: 1000.00,
            depositDate: new Date(),
            purpose: '治疗预付款',
            notes: '测试预付款',
          },
        })
      })

      it('should update deposit status', async () => {
        const updateData = { status: 'used' }
        const request = new NextRequest(`http://localhost:3000/api/deposits/${testDeposit.id}`, {
          method: 'PATCH',
          body: JSON.stringify(updateData),
          headers: { 'Content-Type': 'application/json' },
        })

        const response = await updateDeposit(request, { params: Promise.resolve({ id: testDeposit.id }) })
        const result = await response.json()

        expect(response.status).toBe(200)
        expect(result.status).toBe('used')
      })

      it('should validate status transitions', async () => {
        // First set to used
        await payload.update({
          collection: 'deposits',
          id: testDeposit.id,
          data: { status: 'used', usedAmount: 1000.00, remainingAmount: 0.00 },
        })

        // Try to change back to active (should be invalid)
        const updateData = { status: 'active' }
        const request = new NextRequest(`http://localhost:3000/api/deposits/${testDeposit.id}`, {
          method: 'PATCH',
          body: JSON.stringify(updateData),
          headers: { 'Content-Type': 'application/json' },
        })

        const response = await updateDeposit(request, { params: Promise.resolve({ id: testDeposit.id }) })
        expect(response.status).toBe(400)
      })
    })

    describe('POST /api/deposits/apply-to-bill', () => {
      beforeEach(async () => {
        testDeposit = await payload.create({
          collection: 'deposits',
          data: {
            patientId: testPatient.id,
            treatmentId: testTreatment.id,
            depositType: 'treatment',
            amount: 1000.00,
            status: 'active',
            usedAmount: 0.00,
            remainingAmount: 1000.00,
            depositDate: new Date(),
            purpose: '治疗预付款',
            notes: '测试预付款',
          },
        })

        // Reset bill remaining amount
        await payload.update({
          collection: 'bills',
          id: testBill.id,
          data: { remainingAmount: 500.00, status: 'confirmed' },
        })
      })

      it('should apply deposit to bill successfully', async () => {
        const applyData = {
          depositId: testDeposit.id,
          billId: testBill.id,
          amount: 300.00,
        }

        const request = new NextRequest('http://localhost:3000/api/deposits/apply-to-bill', {
          method: 'POST',
          body: JSON.stringify(applyData),
          headers: { 'Content-Type': 'application/json' },
        })

        const response = await applyDepositToBill(request)
        const result = await response.json()

        expect(response.status).toBe(200)
        expect(result.appliedAmount).toBe(300.00)

        // Check updated deposit
        const updatedDeposit = await payload.findByID({
          collection: 'deposits',
          id: testDeposit.id,
        })
        expect(updatedDeposit.usedAmount).toBe(300.00)
        expect(updatedDeposit.remainingAmount).toBe(700.00)

        // Check updated bill
        const updatedBill = await payload.findByID({
          collection: 'bills',
          id: testBill.id,
        })
        expect(updatedBill.remainingAmount).toBe(200.00)
      })

      it('should validate deposit has sufficient balance', async () => {
        const applyData = {
          depositId: testDeposit.id,
          billId: testBill.id,
          amount: 1500.00, // Exceeds deposit balance
        }

        const request = new NextRequest('http://localhost:3000/api/deposits/apply-to-bill', {
          method: 'POST',
          body: JSON.stringify(applyData),
          headers: { 'Content-Type': 'application/json' },
        })

        const response = await applyDepositToBill(request)
        expect(response.status).toBe(400)
      })

      it('should update bill status to paid when fully paid with deposit', async () => {
        // Reset bill to have small remaining amount
        await payload.update({
          collection: 'bills',
          id: testBill.id,
          data: { remainingAmount: 200.00 },
        })

        const applyData = {
          depositId: testDeposit.id,
          billId: testBill.id,
          amount: 200.00, // Full remaining amount
        }

        const request = new NextRequest('http://localhost:3000/api/deposits/apply-to-bill', {
          method: 'POST',
          body: JSON.stringify(applyData),
          headers: { 'Content-Type': 'application/json' },
        })

        const response = await applyDepositToBill(request)
        expect(response.status).toBe(200)

        // Check bill status updated to paid
        const updatedBill = await payload.findByID({
          collection: 'bills',
          id: testBill.id,
        })
        expect(updatedBill.remainingAmount).toBe(0)
        expect(updatedBill.status).toBe('paid')
      })

      it('should update deposit status to used when fully used', async () => {
        // Create a deposit with small amount
        const smallDeposit = await payload.create({
          collection: 'deposits',
          data: {
            patientId: testPatient.id,
            depositType: 'treatment',
            amount: 100.00,
            status: 'active',
            usedAmount: 0.00,
            remainingAmount: 100.00,
            depositDate: new Date(),
            purpose: '小额预付款',
          },
        })

        const applyData = {
          depositId: smallDeposit.id,
          billId: testBill.id,
          amount: 100.00, // Full deposit amount
        }

        const request = new NextRequest('http://localhost:3000/api/deposits/apply-to-bill', {
          method: 'POST',
          body: JSON.stringify(applyData),
          headers: { 'Content-Type': 'application/json' },
        })

        const response = await applyDepositToBill(request)
        expect(response.status).toBe(200)

        // Check deposit status updated to used
        const updatedDeposit = await payload.findByID({
          collection: 'deposits',
          id: smallDeposit.id,
        })
        expect(updatedDeposit.remainingAmount).toBe(0)
        expect(updatedDeposit.status).toBe('used')

        // Clean up
        await payload.delete({ collection: 'deposits', id: smallDeposit.id })
      })
    })
  })
})
