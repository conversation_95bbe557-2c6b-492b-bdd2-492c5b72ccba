import { getPayload, Payload } from 'payload'
import config from '@/payload.config'
import { describe, it, beforeAll, afterAll, beforeEach, expect } from 'vitest'

let payload: Payload
let testUser: any
let testPatient: any
let testBill: any
let testTreatment: any

describe('Deposits Collection Integration Tests', () => {
  beforeAll(async () => {
    const payloadConfig = await config
    payload = await getPayload({ config: payloadConfig })

    // Create test data
    testUser = await payload.create({
      collection: 'users',
      data: {
        email: '<EMAIL>',
        role: 'admin',
        firstName: 'Test',
        lastName: 'User',
        clerkId: 'test-clerk-id',
      },
    })

    testPatient = await payload.create({
      collection: 'patients',
      data: {
        fullName: '测试患者',
        phone: '13800138000',
        email: '<EMAIL>',
        medicalNotes: '无过敏史',
      },
    })

    testTreatment = await payload.create({
      collection: 'treatments',
      data: {
        name: '测试治疗',
        description: '测试治疗描述',
        defaultPrice: 500,
        defaultDurationInMinutes: 60,
      },
    })

    testBill = await payload.create({
      collection: 'bills',
      data: {
        patient: testPatient.id,
        treatment: testTreatment.id,
        billType: 'treatment',
        description: '测试账单',
        subtotal: 500.00,
        discountAmount: 0.00,
        taxAmount: 0.00,
        totalAmount: 500.00,
        remainingAmount: 500.00,
        status: 'confirmed',
        dueDate: new Date('2024-12-31'),
        createdBy: testUser.id,
      },
    })
  })

  afterAll(async () => {
    // Clean up test data
    if (testBill) await payload.delete({ collection: 'bills', id: testBill.id })
    if (testTreatment) await payload.delete({ collection: 'treatments', id: testTreatment.id })
    if (testPatient) await payload.delete({ collection: 'patients', id: testPatient.id })
    if (testUser) await payload.delete({ collection: 'users', id: testUser.id })
  })

  describe('Deposits Collection', () => {
    let testDeposit: any

    beforeEach(async () => {
      // Clean up any existing deposits
      const existingDeposits = await payload.find({ collection: 'deposits' })
      for (const deposit of existingDeposits.docs) {
        await payload.delete({ collection: 'deposits', id: deposit.id })
      }
    })

    describe('Create Deposit', () => {
      it('should create a new deposit successfully', async () => {
        const depositData = {
          patientId: testPatient.id,
          treatmentId: testTreatment.id,
          depositType: 'treatment',
          amount: 1000.00,
          status: 'active',
          usedAmount: 0.00,
          remainingAmount: 1000.00,
          depositDate: new Date(),
          purpose: '治疗预付款',
          notes: '年度套餐预付',
          expiryDate: new Date('2024-12-31T00:00:00Z'),
        }

        const result = await payload.create({
          collection: 'deposits',
          data: depositData,
        })

        expect(result.id).toBeDefined()
        expect(result.depositNumber).toMatch(/^DEP-\d{4}-\d{3}$/)
        expect(result.patientId).toBe(testPatient.id)
        expect(result.amount).toBe(1000.00)
        expect(result.status).toBe('active')
        expect(result.usedAmount).toBe(0)
        expect(result.remainingAmount).toBe(1000.00)

        testDeposit = result
      })

      it('should validate required fields', async () => {
        const invalidDepositData = {
          // Missing required fields
          depositType: 'treatment',
          amount: 500.00,
        }

        try {
          await payload.create({
            collection: 'deposits',
            data: invalidDepositData,
          })
          expect(true).toBe(false) // Should not reach here
        } catch (error) {
          expect(error).toBeDefined()
        }
      })
    })

    describe('Find Deposits', () => {
      beforeEach(async () => {
        // Create test deposit
        testDeposit = await payload.create({
          collection: 'deposits',
          data: {
            patientId: testPatient.id,
            treatmentId: testTreatment.id,
            depositType: 'treatment',
            amount: 1000.00,
            status: 'active',
            usedAmount: 200.00,
            remainingAmount: 800.00,
            depositDate: new Date(),
            purpose: '治疗预付款',
            notes: '测试预付款',
          },
        })
      })

      it('should fetch deposits with pagination', async () => {
        const result = await payload.find({
          collection: 'deposits',
          page: 1,
          limit: 10,
        })

        expect(result.docs).toBeDefined()
        expect(result.totalDocs).toBeGreaterThan(0)
        expect(result.page).toBe(1)
        expect(result.limit).toBe(10)
      })

      it('should filter deposits by patient ID', async () => {
        const result = await payload.find({
          collection: 'deposits',
          where: {
            patientId: {
              equals: testPatient.id,
            },
          },
        })

        expect(result.docs.every((deposit: any) => deposit.patientId === testPatient.id)).toBe(true)
      })
    })

    describe('Update Deposit', () => {
      beforeEach(async () => {
        testDeposit = await payload.create({
          collection: 'deposits',
          data: {
            patientId: testPatient.id,
            treatmentId: testTreatment.id,
            depositType: 'treatment',
            amount: 1000.00,
            status: 'active',
            usedAmount: 0.00,
            remainingAmount: 1000.00,
            depositDate: new Date(),
            purpose: '治疗预付款',
            notes: '测试预付款',
          },
        })
      })

      it('should update deposit status', async () => {
        const result = await payload.update({
          collection: 'deposits',
          id: testDeposit.id,
          data: { status: 'used' },
        })

        expect(result.status).toBe('used')
      })

      it('should update deposit amounts', async () => {
        const result = await payload.update({
          collection: 'deposits',
          id: testDeposit.id,
          data: {
            usedAmount: 300.00,
            remainingAmount: 700.00,
          },
        })

        expect(result.usedAmount).toBe(300.00)
        expect(result.remainingAmount).toBe(700.00)
      })
    })

  })
})
