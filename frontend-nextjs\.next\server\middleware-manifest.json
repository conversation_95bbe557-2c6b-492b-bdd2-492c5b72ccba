{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules__pnpm_f70aea8e._.js", "server/edge/chunks/[root-of-the-server]__92ca1197._.js", "server/edge/chunks/edge-wrapper_26561c45.js", "server/edge/chunks/_14c1c4d5._.js", "server/edge/chunks/a5167_@clerk_backend_dist_a1e27434._.js", "server/edge/chunks/79fbd_@clerk_shared_dist_f7e95f9b._.js", "server/edge/chunks/4e4c4_@clerk_nextjs_dist_esm_4e5142ff._.js", "server/edge/chunks/node_modules__pnpm_fac582cc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5ff26227.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6iMNFxJQRw5kwyobQ2E/Q8u2QleJW6yuaZTizsNE3Gw=", "__NEXT_PREVIEW_MODE_ID": "24d6884e5966d05356b7ad111d6392b6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "95f8e455a53b20ead8ff2bc59c8df28e9442c9eceef9baed8b5d055cb0455382", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "437b75547c55b450ef8e65f9f0efadc6810014100f472a6538138bbd42acc55e"}}}, "sortedMiddleware": ["/"], "functions": {}}